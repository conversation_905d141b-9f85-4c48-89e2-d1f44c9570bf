{"Common": {"contactUs": "Contact Us", "getStarted": "Get Started", "learnMore": "Learn More", "bookMeeting": "Book Meeting", "viewPricing": "View Pricing", "ourServices": "Our Services", "talkToHuman": "Talk to Human"}, "Navigation": {"home": "Home", "about": "About Us", "services": "Services", "testimonials": "Testimonials", "faq": "FAQ", "contact": "Contact Us", "approach": "Approach", "websiteDevelopment": "Website Development", "chatbotIntegration": "Chatbot Integration", "menu": "<PERSON><PERSON>", "menuSubtitle": "Start by discussing your goals, challenges, and preferences.", "conceptIdeation": "Concept & Ideation", "conceptDescription": "We brainstorm and develop a detailed plan for your site.", "projectKickoff": "Project Kickoff", "freeConsultation": "Free Consultation", "websiteDevelopmentDesc": "Custom websites built with modern technologies", "chatbotIntegrationDesc": "AI-powered chatbots for customer engagement"}, "Footer": {"references": "References", "services": "Services", "socialNetworks": "Social networks", "contact": "Contact", "approach": "Approach", "aboutUs": "About us", "testimonials": "Testimonials", "faq": "FAQ", "contactUs": "Contact us", "websiteDevelopment": "Website development", "chatbotIntegration": "Chatbot Integration", "linkedin": "LinkedIn", "newsletter": "Subscribe to our newsletter", "copyright": "© 2025 UpZera. All rights reserved."}, "Chatbot": {"viewPricing": "View Pricing", "viewPricingDesc": "View our pricing and packages", "bookMeeting": "Book Meeting", "bookMeetingDesc": "Schedule a free consultation", "ourServices": "Our Services", "ourServicesDesc": "Learn about our solutions", "talkToHuman": "Talk to Human", "talkToHumanDesc": "Get personalized help", "welcomeMessage": "Hi there! 👋 I'm the UpZera assistant.", "welcomeDescription": "We build smart digital tools that move businesses forward. What would you like to know about?", "onlineStatus": "Online Now", "offlineStatus": "Offline", "replyPlaceholder": "Reply to UpBot", "quickActionsTitle": "What would you like to know about?", "quickActionServices": "🚀 Our Services", "quickActionPricing": "💰 Pricing", "quickActionBookMeeting": "📅 Book Meeting", "quickActionPortfolio": "💼 Portfolio", "quickActionServicesMessage": "Tell me about your services", "quickActionPricingMessage": "What are your prices?", "quickActionConsultationMessage": "I want to book a consultation", "quickActionPortfolioMessage": "Show me your previous work", "serviceOptionsTitle": "Choose a service to learn more:", "serviceWebDevelopment": "Full-Stack Web Development", "serviceChatbots": "Chatbot Lead Generation", "serviceWebDevelopmentMessage": "Tell me about your web development services", "serviceChatbotsMessage": "I want to know about your chatbot services", "leadFormTitle": "Contact Information", "leadFormName": "Name", "leadFormEmail": "Email", "leadFormSubmit": "Submit", "leadFormSubmitting": "Submitting...", "leadFormErrorEmail": "Please enter a valid email", "leadFormErrorName": "Please enter your name", "leadFormErrorSubmit": "Failed to submit. Please try again later.", "leadFormSuccess": "Thank you {name}! Our team will contact you shortly.", "bookingMessage": "Perfect! I'd love to help you schedule a consultation. Let me show you our booking calendar:", "servicesMessage": "Great! Here are our main services. Click on any service to learn more:", "supportTicketStart": "I'd be happy to connect you with our team! 🤝\n\nI'll help you create a support ticket so our team can provide personalized assistance.\n\nFirst, could you please tell me your **name**?", "supportTicketNamePrompt": "Great! Now, could you please provide your **email address**?", "supportTicketEmailPrompt": "Perfect! Now, please describe the issue or question you'd like our team to help you with:", "supportTicketConfirm": "Thanks! Let me confirm your support ticket details:\n\n**Name:** {name}\n**Email:** {email}\n**Issue:** {description}\n\nShould I create this support ticket for you?", "supportTicketSuccess": "Perfect! 🎉 Your support ticket has been created successfully.\n\n**Ticket Details:**\n• **Name:** {name}\n• **Email:** {email}\n• **Issue:** {description}\n\n📧 **Next Steps:**\n• Our team will review your request\n• You'll receive a response within 24 hours\n• We'll contact you at the provided email address\n\nThank you for reaching out to UpZera!", "supportTicketError": "I apologize, but there was an issue creating your support ticket. Please try again or contact us <NAME_EMAIL>.", "supportTicketRestart": "No problem! Would you like to start over with the support ticket information?", "supportTicketRestartConfirm": "Great! Let's start over. Could you please tell me your **name**?", "confirmDetailsPrompt": "Great! 🎉 I'm connecting you with our team.\n\n📞 **Next Steps:**\n• Someone will reach out to you shortly\n• We'll contact you at the provided email address\n\nThank you for choosing UpZera!", "reenterDetailsPrompt": "No problem! Would you like to re-enter your details?", "invalidEmailMessage": "That doesn't look like a valid email address. Could you please provide a valid email?", "pleaseConfirmMessage": "Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.", "pleaseConfirmReenterMessage": "Please answer with 'yes' if you'd like to re-enter your details, or 'no' if you'd prefer to do something else.", "pleaseConfirmTicketMessage": "Please answer with 'yes' to create the support ticket, or 'no' if you'd like to make changes.", "pleaseConfirmRestartMessage": "Please answer with 'yes' if you'd like to start over, or 'no' if you'd prefer to do something else.", "endConversationConfirm": "I understand you'd like to end our conversation. Are you sure you don't need any other assistance today?", "endConversationGoodbye": "It was a pleasure to chat with you! Feel free to reach out anytime if you have more questions. Have a great day! 👋", "continueConversation": "Perfect! I'm here to help. What would you like to know more about?", "anythingElseYes": "Great! What else would you like to know?", "anythingElseNo": "I understand! Before we end our chat, are you sure you don't need any other assistance today? 🤔", "helpMenuResponses": ["I'd be happy to help you with that! Here are the main ways I can assist you:", "Let me help you find what you're looking for! Here are the main services I can assist with:", "I'm here to help! Here are the main areas where I can provide assistance:", "Great question! Let me show you the main ways I can help:", "I'd love to assist you! Here are the key areas I can help with:", "No problem! Here are the main services I can help you with:", "I'm ready to help! Here are the primary ways I can assist you:", "Let me guide you to the right place! Here are the main options:"], "followUpPricing": "Would you like to know more about any specific service pricing?", "followUpServices": "Would you like to know more about our other services?", "bookingYesResponse": "Great! To book a meeting, please let me know your preferred date and time, or you can check our booking calendar for availability.", "aiErrorMessage": "I'm having trouble connecting right now. Try asking about our services or book a free consultation!", "aiErrorFallback": "Let me connect you with our team! You can book a free consultation or email <NAME_EMAIL>.", "fallbackHelpMessage": "I'd be happy to help you with that! Let me show you what I can assist you with:", "restartConversation": "Start New Conversation", "conversationEnded": "Conversation Ended", "mainMenuPricesMessage": "I want to know about your prices", "mainMenuBookingMessage": "I want to book a meeting", "mainMenuServicesMessage": "Tell me about your services", "mainMenuHumanMessage": "I need to speak with a human", "wouldYouLikeToKnowMoreServices": "Would you like to know more about our other services?", "wouldYouLikeToKnowMorePricing": "Would you like to know more about any specific service pricing?", "greatToBookMeeting": "Great! To book a meeting, please let me know your preferred date and time, or you can check our booking calendar for availability.", "makeSureUnderstandCorrectly": "I want to make sure I understand correctly. Do you need any other assistance today, or are you ready to end our chat?", "happyToHelpMainWays": "I'd be happy to help you with that! Here are the main ways I can assist you:", "letMeHelpFindServices": "Let me help you find what you're looking for! Here are the main services I can assist with:", "hereToHelpMainAreas": "I'm here to help! Here are the main areas where I can provide assistance:", "greatQuestionMainWays": "Great question! Let me show you the main ways I can help:", "loveToAssistKeyAreas": "I'd love to assist you! Here are the key areas I can help with:", "noProblemMainServices": "No problem! Here are the main services I can help you with:", "readyToHelpPrimaryWays": "I'm ready to help! Here are the primary ways I can assist you:", "letMeGuideMainOptions": "Let me guide you to the right place! Here are the main options:", "understandEndConversation": "I understand you'd like to end our conversation. Are you sure you don't need any other assistance today?", "happyToHelpShowAssist": "I'd be happy to help you with that! Let me show you what I can assist you with:", "greatWhatElseKnow": "Great! What else would you like to know?", "understandBeforeEndChat": "I understand! Before we end our chat, are you sure you don't need any other assistance today? 🤔", "invalidEmailAddress": "That doesn't look like a valid email address. Could you please provide a valid email?", "greatConnectingTeam": "Great! 🎉 I'm connecting you with our team.\n\n📞 **Next Steps:**\n• Someone will reach out to you shortly\n• We'll contact you at the provided email address\n\nThank you for choosing UpZera!", "noProblemReenterDetails": "No problem! Would you like to re-enter your details?", "pleaseAnswerYesNoCorrect": "Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.", "pleaseAnswerYesNoReenter": "Please answer with 'yes' if you'd like to re-enter your details, or 'no' if you'd prefer to do something else.", "apologizeIssueCreatingTicket": "I apologize, but there was an issue creating your support ticket. Please try again or contact us <NAME_EMAIL>.", "noProblemStartOverTicket": "No problem! Would you like to start over with the support ticket information?", "pleaseAnswerYesNoCreateTicket": "Please answer with 'yes' to create the support ticket, or 'no' if you'd like to make changes.", "greatStartOverName": "Great! Let's start over. Could you please tell me your **name**?", "pleaseAnswerYesNoStartOver": "Please answer with 'yes' if you'd like to start over, or 'no' if you'd prefer to do something else.", "pleasureToChat": "It was a pleasure to chat with you! Feel free to reach out anytime if you have more questions. Have a great day! 👋", "perfectHereToHelp": "Perfect! I'm here to help. What would you like to know more about?", "letUsKnowAnythingElse": "Let us know if you need anything else! 😊", "areYouStillThere": "Are you still there? I'm here if you need any help! 😊", "beenAwayForAWhile": "It looks like you've been away for a while. Feel free to start a new conversation anytime you need help! 👋", "noProblemAnythingElse": "No problem! Is there anything else I can help you with?", "gotIt": "Got it!", "happyToHelpLearnServices": "I'd be happy to help you learn more about our services.", "anythingElseToday": "Is there anything else I can help you with today?", "sureStartOverName": "Sure! Let's start over. What's your name?", "noWorriesAnythingElse": "No worries! Is there anything else I can help you with?", "noProblemAnythingElseToday": "No problem! Is there anything else I can help you with today?", "chatHasEnded": "Cha<PERSON> has ended", "startNewChat": "Start new chat"}, "FAQ": {"title": "Frequently Asked Questions", "description": "Find answers to common questions about our services and approach", "q1": "What kind of businesses do you work with?", "a1": "We typically work with startups, small teams, and growing businesses that need fast, reliable digital tools — from landing pages to more complex designs. Whether you're just starting or scaling, we're here to help.", "q2": "How much do your services cost?", "a2": "You'll receive a clear, personalized quote after a free consultation.<br />Our pricing is project-based and depends on your specific goals and scope. Most projects range from €350–€500 for landing pages, €1,400-€2,400 for full websites, and €500-€1,200 for chatbot setups.", "q3": "How fast can you deliver a project?", "a3": "Timelines vary based on complexity, but most landing pages are ready within a few days, while full builds typically take 2–4 weeks. We'll give you a clear timeline before we start — and we stick to it.", "q4": "Do you offer ongoing support?", "a4": "Absolutely — but only when you need it. We're here post-launch for fixes, updates, or improvements. You won't be handed off to a helpdesk — just reach out and we'll help.", "q5": "What makes you different from other web development teams?", "a5": "We're engineers at heart, obsessed with clean solutions. We use agentic AI workflows to deliver faster without sacrificing quality — and we tailor everything to your business, not some cookie-cutter template.", "q6": "How do I get started?", "a6": "Simple — just book a free consultation. We'll talk about your goals, assess your needs, and give you a custom game plan.", "q7": "How do you handle invoicing and payment?", "a7": "<div class=\"space-y-4\"><p>We keep payments simple and transparent:</p><ul class=\"list-disc list-inside space-y-2 pl-4\"><li><b>Free concept preview</b> - We'll create a small design preview to ensure we're the right fit</li><li><b>Contract signing</b> - If you like the direction, we'll formalize the agreement</li><li><b>Deposit payment</b> - 20% for small projects or milestone-based for larger ones</li><li><b>Clear invoicing</b> - Based on agreed milestones or final delivery</li><li><b>Flexible terms</b> - Standard NET15 payment terms, but we can accommodate your needs</li></ul><p>No surprises — just straightforward payment terms that work for both of us.</p></div>", "q8": "What do you need to start a project?", "a8": "<div class=\"space-y-4\"><p>Our streamlined 5-step process:</p><ol class=\"list-decimal list-inside space-y-3 pl-4\"><li class=\"font-medium\">Intro Call<br /><span class=\"font-normal text-purple-100/80\">We'll discuss your vision and requirements</span></li><li class=\"font-medium\">Optional Preview<br /><span class=\"font-normal text-purple-100/80\">Free concept demo to ensure alignment</span></li><li class=\"font-medium\">Agreement & Deposit<br /><span class=\"font-normal text-purple-100/80\">Sign contract and pay deposit</span></li><li class=\"font-medium\">Materials Handoff<br /><span class=\"font-normal text-purple-100/80\">Share your assets and access details</span></li><li class=\"font-medium\">Build Phase<br /><span class=\"font-normal text-purple-100/80\">We develop your solution efficiently</span></li></ol><p>Simple, transparent, and focused on delivering exactly what you need.</p></div>", "stillHaveQuestions": "Still have questions?", "stillHaveQuestionsDesc": "We're here to help with any questions you might have about our services"}, "HomePage": {"heroTitle": "We Build Digital Solutions That Grow Your Business with", "heroHighlight": "zero effort", "heroSubtitle": "UpZera helps you scale with AI-powered solutions that bring together websites, chatbots into a single seamless experience.", "launchWithUs": "Launch with us", "freeConsultation": "Free consultation", "fastTurnaround": "Fast turnaround", "coreServicesTitle": "Our Core Services", "fullStackTitle": "Full-Stack Web Development", "fullStackDesc": "From design to backend logic — we build fully functional websites that look great and work even better.", "chatbotsTitle": "Chatbots & Lead Generation", "chatbotsDesc": "Capture leads, qualify them, and respond instantly with custom-built chatbots that work 24/7.", "supportTitle": "Ongoing Support", "supportDesc": "We don't disappear after launch — we're here to maintain, improve, and grow your solution with you.", "deploymentTitle": "Lightning Fast Deployment", "deploymentDesc": "Get your solutions up and running quickly with our streamlined deployment process.", "techStackTitle": "Our", "techStackHighlight": "Tech Stack", "techStackDesc": "We leverage modern technologies to build robust, scalable, and high-performance web solutions.", "whyChooseTitle": "Why Choose UpZera?", "otherSpecialists": "Other Specialists", "limitedResultsTitle": "Limited results and high costs:", "limitedResultsDesc": "Charge premium prices for basic solutions, focus on appearance over performance/scalability, don't tailor service.", "confusingProcessTitle": "Confusing process, unclear ownership:", "confusingProcessDesc": "Overcomplicate with jargon, unclear who builds the product, slow handoffs.", "oneWayCommTitle": "One-way communication:", "oneWayCommDesc": "Hard to reach, slow replies, inflexible working methods.", "weAtUpZera": "We at UpZera are", "affordableTitle": "Affordable, tailored, and full-stack:", "affordableDesc": "Fair pricing, full-cycle solutions, customized to your needs.", "clearProcessTitle": "Clear process, direct access:", "clearProcessDesc": "Built by us, simple explanations, clear timelines, always know the status.", "personalTitle": "Personal and responsive:", "personalDesc": "Direct access to makers, quick communication via preferred channels, collaborative and transparent.", "ctaTitle": "Got an idea? Let's explore it together.", "ctaDesc": "Contact us to schedule a free project consultation.", "useFreeConsultation": "Use a free consultation"}, "AboutPage": {"heroTitle": "So… who are we, really?", "heroSubtitle": "A team that's more about people than pixels. We build with care, clarity, and a whole lot of curiosity.", "ourStoryTitle": "Our Story", "story1": "UpZera kicked off in 2025 with one mission: build smart digital tools that actually move businesses forward — fast, clean, and with purpose.", "story2": "We're a lean, tech-obsessed team building websites and chatbots. By integrating agentic AI workflows into our process, we're able to deliver faster without ever cutting corners — speed and reliability, side by side.", "story3": "We take you from idea to launch — and when you need us, we're ready to step in and keep things running smooth. No vanishing acts. No handoffs to some faceless support line. Just real people, ready to help when it counts.", "story4": "Every solution we ship is tailor-made — no bloated templates, no fluff. Just smart, clean builds designed around your business, your story, and your next big move.", "tagline": "✨ Elevate your business with Zero effort.", "teamTitle": "Led by <PERSON><PERSON>ous Engineers", "teamSubtitle": "(WILL CHANGE) UpZera is led by two passionate engineering students — one in Electrical Engineering, the other in Automotive. We combine technical depth with design sense to craft reliable, efficient, and modern solutions.", "edgarasRole": "Co-founder", "edgarasDesc": "Electrical Engineering student with a passion for clean code and efficient systems.", "valuesTitle": "What We Believe In", "honestyTitle": "<PERSON><PERSON><PERSON>", "honestyDesc": "We say what we mean, and we build what we promise.", "responsibilityTitle": "Responsibility", "responsibilityDesc": "Your project is our project. We treat it with the same care.", "clientCareTitle": "Client Care", "clientCareDesc": "We listen first, build second. Your needs come first, always.", "trustworthinessTitle": "Trustworthiness", "trustworthinessDesc": "We believe trust is earned. We're here for the long run.", "servicesTitle": "Full-Cycle Development, Built for Growth", "webDevTitle": "Web Development", "webDesign": "Web Design", "uiUxImplementation": "UI/UX Implementation", "backendTitle": "Backend & Automations", "backendLogic": "Backend Logic", "processAutomation": "Process Automation", "chatbotsTitle": "Chatbots & Lead Tools", "aiIntegrations": "AI Integrations", "leadGeneration": "Lead Generation", "integrationsTitle": "Smart Integrations", "apis": "APIs", "calendarBooking": "Calendar/Booking tools", "performanceTitle": "Performance & SEO", "optimization": "Optimization", "searchVisibility": "Search Visibility", "supportTitle": "Ongoing Support", "maintenance": "Maintenance", "updatesImprovements": "Updates & Improvements", "ctaFinalTitle": "Enough About Us — Let's Talk About You", "ctaFinalDesc": "Ready to elevate your digital presence? Let's start a conversation about your business goals.", "getFreeQuote": "Get Your Free Quote"}, "ContactPage": {"heroTitle": "Do not hesitate to get in touch", "heroSubtitle": "We will do our best to answer all your questions and provide you with our services", "email": "Email", "phone": "Phone", "address": "Address", "contactUs": "Contact us", "name": "Name", "namePlaceholder": "Your name", "emailPlaceholder": "<EMAIL>", "service": "Service", "message": "Message", "messagePlaceholder": "Your message", "sending": "Sending...", "contact": "Contact", "chatbotIntegration": "Chatbot Integration", "chatbotIntegrationDesc": "AI-powered chat solutions", "webDevelopment": "Web Development", "webDevelopmentDesc": "Custom website solutions", "otherServices": "Other Services", "otherServicesDesc": "Custom requirements", "mvpVirtualAssistant": "MVP Virtual Assistant", "mvpVirtualAssistantDesc": "Essential chatbot features for your business", "customizableAiAssistant": "Customizable AI Assistant", "customizableAiAssistantDesc": "Advanced features with deep integrations", "websiteEssentials": "Website Essentials", "websiteEssentialsDesc": "Basic website with core functionality", "smartBusinessWebsites": "Smart Business Websites", "smartBusinessWebsitesDesc": "Enhanced features for growing businesses", "advancedWebPlatforms": "Advanced Web Platforms", "advancedWebPlatformsDesc": "Complex solutions with custom functionality", "selectedServices": "Selected Services", "nameRequired": "Name is required.", "emailRequired": "Email is required.", "emailInvalid": "<PERSON><PERSON> is invalid.", "serviceRequired": "Please select at least one service.", "messageRequired": "Message is required.", "thankYou": "Thank you! Your message has been sent.", "orTitle": "OR!", "scheduleMeeting": "Schedule a Meeting", "scheduleMeetingDesc": "Book a free consultation call with our team"}, "ApproachPage": {"title": "Our Approach", "subtitle": "We plan, build, and support your project every step of the way.", "step1Title": "1. Discovery Call", "step1Description": "We start with a short conversation to understand your goals, challenges, and vision for the project.", "step2Title": "2. Concept & Optional Demo", "step2Description": "We brainstorm ideas and, if requested, provide a small, free of charge, demo to make sure we're aligned before moving forward.", "step3Title": "3. Scope, Pricing & Payment", "step3Description": "We define the project scope, timeline, and pricing. Once agreed, we sign the contract and collect a deposit.", "step4Title": "4. <PERSON>", "step4Description": "With everything in place, development begins. We schedule milestones and get to work.", "step5Title": "5. Build & Feedback", "step5Description": "We build in stages, sharing progress and collecting your input to keep things on track and on point.", "step6Title": "6. Delivery & Support", "step6Description": "We finalize and hand over your project, offer support, and stay available for future collaboration."}, "Newsletter": {"emailPlaceholder": "Your email address", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "successMessage": "Thank you for subscribing to our newsletter!"}, "TestimonialsPage": {"heroTitle": "Client Success Stories", "heroSubtitle": "See how we've helped businesses transform their digital presence and achieve their goals.", "featuredProject": "Featured Project: Qochi Services", "projectTitle": "How We Helped Qochi Launch a Clean, Modern Tutoring Platform", "testimonial1Quote": "UpZera made the entire process seamless. The site is fast, beautiful, and our students love how easy it is to book sessions.", "testimonial1Author": "— Founder of Qochi Services", "checkItOut": "Check it out →", "challengeTitle": "The Challenge", "challengeDescription": "Qochi needed a clean, modern website to showcase their tutoring services — with the ability for students to easily book sessions online and engage with the brand.", "deliveredTitle": "What We Delivered", "delivered1Title": "Responsive, fast-loading website", "delivered1Description": "With a sleek, professional layout", "delivered2Title": "Booking integration using Calendly", "delivered2Description": "Allowing clients to reserve tutoring sessions effortlessly", "delivered3Title": "Thoughtful content layout", "delivered3Description": "Focused on trust, clarity, and conversion", "delivered4Title": "Simple, elegant design system", "delivered4Description": "That reflects the educational tone of <PERSON><PERSON><PERSON>'s brand", "techStackTitle": "Tech Stack", "resultsTitle": "Results", "result1": "Live in just a few days", "result2Title": "Fully mobile-friendly", "result2Description": "Perfect scores on mobile usability tests", "result3": "Smooth booking experience", "result4": "Owner can manage everything with ease", "clientQuoteTitle": "From the Client", "clientQuote": "The UpZera team made it feel effortless. They understood exactly what I needed, and delivered even better than I imagined.", "ctaTitle": "Want to be our next success story?", "ctaDescription": "Let's turn your idea into a live product — fast, custom, and fully done-for-you.", "ctaButton": "Book your free consultation now!"}}